CREATE OR REPLACE FUNCTION public.tms_create_service_request(form_data_ json, entry_id integer DEFAULT 0)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
	-- Bare minimums
	status boolean;
	message text;
	affected_rows integer;
	validation_resp text[];
	resp_data json;
	ip_address_ text;
	user_agent_ text;
	org_id_ integer;
	usr_id_ uuid;
	srvc_type_id_ integer;
	timeline_comment text;
	
	-- Form data
--	srvc_statuses_default json default '{ "ACTIVE":[ { "key":"open", "title":"Open", "color" : "#fa8c16" } ], "DONE":[ ], "CLOSED":[ { "key": "closed", "title": "Closed", "color" : "#E1E1E1" } ] }';
--	srvc_type_name text;
--	srvc_type_key text;
--	srvc_type_desc text;
--	srvc_type_icon_selector text;
	
	-- temp
	cust_creation_resp json;
	cust_id_in_db uuid;
    code_prefix text;
	request_priority_ text;
	display_code text;
	transition_date_ timestamp without time zone;
	transition_log_res json;
	history_res json;
	existing_db_row json;
	existing_form_data json;
	key_   text;
   	value_ text;
    status_key_frm_form text;
    old_status_title text;
   	new_status_title text;
    comment_ text;
    update_for_comment boolean DEFAULT false;
    send_for_discount_approval boolean DEFAULT false;
    sp_send_for_discount_approval boolean DEFAULT false;
    assign_to_srvc_prvdr int;
  	_feedback_token text;
    display_code_ text;
   	srvc_status_key_existence text;
   	srvc_type_prefix_custom_field text;
    custom_code_seperator text default '-';
   	is_deleted_ boolean;
    cust_pincode_ text;
    line_items_revisions_data json;
    srvc_req_his_type_ text;
	-- Output
	ins_id integer;

	srvc_type_id_org integer;
	usr_org_id integer default 0;
	usr_organization_org_type text;
	possible_srvc_prvdr int[];
	srvc_prvdr_to_add_req bool;
    login_usr_org_should_possible_srvc_prvdr bool;
    user_context_json json;
    initial_status_update_json json;
    assigned_srvc_prvdr_json json;

	creation_date_ timestamp;
	_config_data json;
	is_cust_access integer;

	cust_ids_resp uuid;
	existing_cust_mob_no text;
	wrkflw_resp_data json;
	srvc_type_details_ json;

	--srvc status wise mandatory fields
	srvc_status_mandatory_fields json;
	custom_fields_json json;
	status_mandatory_fields text[];
    mandatory_fields_fr_status text;
    _mandatory_single_entry text;
   	_missing_fields_fr_status text[];
    is_frm_frontend_ bool;
  	_single_col_details json;
 	_missing_fields_fr_statuses text[];
 
	--discount section section
	discount_approval_status text;
	sp_discount_approval_status text;

	--srvc req locked /unlocked section 
	srvc_req_lock_status text;

	--sp_srvc_type_his_db_id
	sp_srvc_type_his_db_id int;

	--collab_order_id
	collab_order_id_ text;
	validation_resp_ json;
	org_level_settings_data json;
	code_suffix text;

  	_delete_srvc_req_proto jsonb default '{}';
	subtask_deletion_resp_ json default '{}';
	srvc_req_address text;

	profit_and_loss_data_ json;
    is_sp_reassgined bool default false;
   
    feature_access_fr_TMS250414073641 boolean;
   	is_restrict_view_ bool;

	vertical_id_ int;
	prvdr_srvc_hub_ bigint;
	_temp_is_bulk_line_items_update int;
	is_line_item_updated_ int;
	_temp_is_agent_update bool;

	is_org_srvc_prvdr_ bool;
	-- Booking related variables
	capacity_id_ bigint;
	booking_details_ jsonb;
	booking_id_ int;
    total_booked_qty_ int;
	booking_available_ boolean DEFAULT true;
	feature_access_fr_TMS250620631024 boolean;

	-- Booking status variables for timeline
	is_booked_ boolean;
	booking_message_ text;

begin
	status = false;
	message = 'Internal_error';

	org_id_ = json_extract_path_text(form_data_,'org_id');
	usr_id_ = json_extract_path_text(form_data_,'usr_id');
	ip_address_ = json_extract_path_text(form_data_,'ip_address');
	user_agent_ = json_extract_path_text(form_data_,'user_agent');
	srvc_type_id_ = json_extract_path_text(form_data_,'srvc_type_id');
	is_cust_access = form_data_->>'is_customer_access';
	is_frm_frontend_ = form_data_->>'is_frm_frontend';
	
--	raise notice 'Service id %',srvc_type_id_;
	-- Form data
	request_priority_ = json_extract_path_text(form_data_,'request_priority');
    status_key_frm_form = json_extract_path_text(form_data_,'new_status');
   
    is_deleted_ = form_data_->>'update_deletion';          
    cust_pincode_ = form_data_->>'cust_pincode';
    update_for_comment = json_extract_path_text(form_data_,'update_for_comment')::boolean;
    send_for_discount_approval = json_extract_path_text(form_data_,'send_for_discount_approval')::boolean;
    sp_send_for_discount_approval = json_extract_path_text(form_data_,'sp_send_for_discount_approval')::boolean;
    timeline_comment =  left(json_extract_path_text(form_data_::json,'comment'),80);
   	_feedback_token = form_data_->>'feedback_token';
  	_temp_is_agent_update = (form_data_->>'is_agent_update')::bool;
  	is_org_srvc_prvdr_ = tms_hlpr_is_org_srvc_prvdr(org_id_);
    --creation date
    creation_date_ = form_data_->>'creation_date';
    raise notice 'form_data_->>>>>>>>>>>>%',form_data_;
    --collab_order_id
    collab_order_id_ = form_data_->>'79a88c7b-c64f-46c4-a277-bc80efa1c154';
   
   	--get vertical_id
    vertical_id_ = form_data_->>'vertical_id';
	prvdr_srvc_hub_ = form_data_->>'prvdr_srvc_hub';
	_temp_is_bulk_line_items_update = (form_data_->>'is_bulk_line_items_update')::int;

	-- Extract booking related data
	capacity_id_ = (form_data_->>'capacity_id')::bigint;
	booking_details_ = form_data_->>'booking_data';
	booking_id_ = form_data_->>'start_booking_id';
    total_booked_qty_ = form_data_->>'total_booked_qty';
  --raise notice 'booking_id_ %',booking_id_;
	-- Also check for direct capacity_id in form_data
	if capacity_id_ is null then
		capacity_id_ = (form_data_->>'capacity_id')::bigint;
	end if;

	-- Extract booking status variables
	is_booked_ = COALESCE((form_data_->>'is_booked')::boolean, null);
	booking_message_ = form_data_->>'booking_message';


    user_context_json = tms_get_user_context_from_data(form_data_);
	feature_access_fr_TMS250414073641 = tms_hlpr_get_feature_access_fr_current_usr(form_data_, 'TMS250414073641');
	feature_access_fr_TMS250620631024 = tms_hlpr_get_feature_access_fr_current_usr(form_data_, 'TMS250620631024');
    if tms_hlpr_is_org_srvc_prvdr(org_id_) then
    	if form_data_->'sp_line_items' is not null then
    		--Get line_items revisions data
    		line_items_revisions_data = hlpr_tms_get_line_items_revisions(org_id_, form_data_->'sp_line_items', user_context_json);
    	 	--Remove line_items revisions data from from_data	
    		srvc_req_his_type_ = 'SP_LINE_ITEMS_REVISIONS';
			form_data_ = jsonb_set(form_data_::jsonb, '{sp_line_items,revisions}', '[]'::jsonb);
			is_line_item_updated_ = 1;
    	end if;
    	if form_data_->'line_items' is not null then
			is_line_item_updated_ = 1;
        end if;
    else
    	if form_data_->'line_items' is not null then
	    	--Get line_items revisions data
	    	line_items_revisions_data = hlpr_tms_get_line_items_revisions(org_id_, form_data_->'line_items', user_context_json);
	    	--Remove line_items revisions data from from_data
	    	srvc_req_his_type_ = 'LINE_ITEMS_REVISIONS';
			form_data_ = jsonb_set(form_data_::jsonb, '{line_items,revisions}', '[]'::jsonb);
			is_line_item_updated_ = 1;
        end if;
    end if;
   
   --discount
   discount_approval_status    = form_data_->>'discount_approval_status';
   sp_discount_approval_status = form_data_->>'sp_discount_approval_status';
  
   --set form_data for billing_discount_approved_by and date time for brand and sp
    if discount_approval_status is not null then
		form_data_ = jsonb_set(form_data_::jsonb,'{discount_approved_by}',to_jsonb(usr_id_),true);
		form_data_ = jsonb_set(form_data_::jsonb,'{discount_approved_date_time}',to_jsonb(now() at time zone 'utc'),true);
	
	elseif sp_discount_approval_status is not null then
		form_data_ = jsonb_set(form_data_::jsonb,'{sp_discount_approved_by}',to_jsonb(usr_id_),true);
		form_data_ = jsonb_set(form_data_::jsonb,'{sp_discount_approved_date_time}',to_jsonb(now() at time zone 'utc'),true);
    end if;
   --service request locked by and datetime
    if form_data_->>'srvc_req_lock' is not null then
		form_data_ = jsonb_set(form_data_::jsonb,'{srvc_req_locked_by}',to_jsonb(usr_id_),true);
		form_data_ = jsonb_set(form_data_::jsonb,'{srvc_req_locked_date_time}',to_jsonb(now() at time zone 'utc'),true);
	elseif form_data_->>'sp_srvc_req_lock' is not null then
		form_data_ = jsonb_set(form_data_::jsonb,'{sp_srvc_req_locked_by}',to_jsonb(usr_id_),true);
		form_data_ = jsonb_set(form_data_::jsonb,'{sp_srvc_req_locked_date_time}',to_jsonb(now() at time zone 'utc'),true);
    end if;

	--set form_data for send_for_billing_u_by and date time for brand and sp
	if form_data_->>'send_for_billing' is not null then
		form_data_ = jsonb_set(form_data_::jsonb,'{send_for_billing_u_by}',to_jsonb(usr_id_),true);
		form_data_ = jsonb_set(form_data_::jsonb,'{send_for_billing_date_time}',to_jsonb(now() at time zone 'utc'),true);

	elseif form_data_->>'sp_send_for_billing' is not null then
		form_data_ = jsonb_set(form_data_::jsonb,'{sp_send_for_billing_u_by}',to_jsonb(usr_id_),true);
		form_data_ = jsonb_set(form_data_::jsonb,'{sp_send_for_billing_date_time}',to_jsonb(now() at time zone 'utc'),true);
	end if;
  
   --get config_data by srvc_type_id
    select srvc_type.form_data 
  	  from cl_cf_service_types as srvc_type 
     where service_type_id = srvc_type_id_ 
  	  into _config_data;
  	 
	--Get org_level_settings_data by org_id
	org_level_settings_data = tms_hlpr_get_org_level_settings_config_data_for_org(org_id_)->'data';
  	 
--	start of block for status wise mandatory fields
	if status_key_frm_form is not null and
		(_config_data->>'srvc_cust_fields_json' is not null and _config_data->>'srvc_cust_fields_json' <> '' ) then
		if feature_access_fr_TMS250414073641 then
			is_restrict_view_ = tms_hlpr_is_srvc_req_view_restricted(status_key_frm_form,_config_data);
		end if;
	    mandatory_fields_fr_status = 'mandatory_fields_fr_status_'||status_key_frm_form;
		select srvc_types.form_data->mandatory_fields_fr_status
  		  from cl_cf_service_types as srvc_types
  	     where srvc_types.service_type_id = srvc_type_id_	
		  into srvc_status_mandatory_fields;
--		 raise notice 'srvc_status_mandatory_fields %', srvc_status_mandatory_fields;
		 
		form_data_ = jsonb_set(form_data_::jsonb,'{entry_id}',to_jsonb(entry_id),true);
		existing_db_row = tms_get_srvc_req_details(form_data_);
		existing_form_data = existing_db_row::jsonb->'data'->'form_data';
	
		status_mandatory_fields = array( select json_array_elements_text(srvc_status_mandatory_fields))::text[];
		custom_fields_json = ((_config_data->>'srvc_cust_fields_json')::jsonb#>>'{translatedFields}')::jsonb;
--		raise notice 'status_mandatory_fields %', status_mandatory_fields;
--		raise notice 'custom_fields_json %', custom_fields_json;
		  
		FOREACH _mandatory_single_entry IN ARRAY status_mandatory_fields 
		LOOP 
			if (existing_form_data->>_mandatory_single_entry is null and
					(existing_form_data->'attachments'->>_mandatory_single_entry is null or
					 existing_form_data->'attachments'->>_mandatory_single_entry = '[]')
			   ) or
				existing_form_data->>_mandatory_single_entry = '' or 
				existing_form_data->>_mandatory_single_entry = '[]' then 
				
					message = 'srvc_status_missing_fields';
					select value
					  from json_array_elements(custom_fields_json)
					 where _mandatory_single_entry = value->>'key'
					 limit 1
					  into _single_col_details;
					 _missing_fields_fr_status = array_append(_missing_fields_fr_status,_single_col_details->>'label');
			end if;	
	    END LOOP;
--	    raise notice '_missing_fields_fr_status %', _missing_fields_fr_status;
	   
	   	if array_length(_missing_fields_fr_status,1) > 0 then 
	   		--
		   	if is_frm_frontend_ is not true then
		        new_status_title = tms_get_srvc_status_title_by_key(srvc_type_id_, status_key_frm_form);
		   		form_data_ = jsonb_set(form_data_::jsonb,'{request_description}',to_jsonb(_missing_fields_fr_status),true);
		   	
		   		history_res = tms_add_to_srvc_req_timeline(
						srvc_type_id_, entry_id, 'ERROR','Status movement to ' || new_status_title || ' failed, mandatory fields missing ', form_data_);
		    end if;
			return json_build_object('status',status,'code',message,'data',_missing_fields_fr_status); 
		end if;
	end if;
--	end of block for status wise mandatory fields
	  	
  		
	if form_data_->'new_prvdr' is not null then
		assign_to_srvc_prvdr = (form_data_->>'new_prvdr')::int;
		form_data_ = jsonb_set( 
						form_data_::jsonb,
						'{prvdr_assg_timestamp}'::text[],
						to_jsonb(now() at time zone 'utc'),
						true
					);	
		sp_srvc_type_his_db_id = hlpr_tms_valid_and_create_srvc_type_his_or_rtn_ltst_his_db_id(form_data_, assign_to_srvc_prvdr, srvc_type_id_, true);
		if sp_srvc_type_his_db_id is not null then
			form_data_ = form_data_::jsonb || ('{"sp_srvc_type_his_db_id":"'|| sp_srvc_type_his_db_id || '"}')::jsonb;
		end if;
	end if;

   if (form_data_->>'is_prvdr_reassigned')::bool is true then
   		is_sp_reassgined := true;
   		
   end if;

--	if status_key_frm_form is not null then
--
--		select title 
--	      from cl_cf_srvc_statuses as srvc_status
--	     where srvc_status.srvc_id = srvc_type_id_
--           and srvc_status.status_key = status_key_frm_form
--          into new_status_title;
--	end if;

	
	--	we need to validate whether there is a entry with same name/key
--	validation_resp := array ( 
--		 select srvc_type.service_type_id::text 
--		   from cl_cf_service_types srvc_type
--		  where srvc_type.org_id = org_id_
--		    and srvc_type.service_type_id <> entry_id
--		    and (
--		    	srvc_type.service_code = srvc_type_key
--		    	or 
--		    	srvc_type.title = srvc_type_name
--		    )
--	);
-- Process first check if a customer already exists in db for the org(both mobile num and email should not exist in DB)
-- no - add the customer
-- then add the entry to service requests table 
-- get insert id
-- get display code prefix from service type config
-- generate a code for the req
-- update the display code for inserted entry
-- 

	if form_data_->>'request_req_date' = '' then 
		form_data_ = form_data_::jsonb - 'request_req_date';
	end if;

	if array_length(validation_resp,1) > 0 then 
	
		status = false;
		message = 'title_or_key_exists';
	
	elsif entry_id = 0 then
	
		--srvc req validating basic fields
		validation_resp_ = tms_hlpr_srvc_req_validation(_config_data, org_level_settings_data, form_data_);
	  	if (validation_resp_->>'status')::bool is not true then
	  		return json_build_object('status',status,'code',validation_resp_->>'code','data', validation_resp_->>'message');
	  	end if;

		--get srvc_type_id belongs to which org
		select srvc_type_org.org_id 
		  from cl_cf_service_types as srvc_type_org
		 where srvc_type_org.service_type_id = srvc_type_id_
		  into srvc_type_id_org;
		 
		 
		--check if srvc_type_id_org and login usr_org is not same then 
		if srvc_type_id_org <> org_id_ then 
		 
			usr_org_id = org_id_;
		 	--get what is the org_type of the login user's organization
		 	select org_type.org_type 
		 	  from cl_tx_orgs as org_type
		 	 where org_type.org_id = org_id_ --usr_org_id
		 	  into usr_organization_org_type;
		 	 
		 	 --get possible_srvc_prvdr for srvc_type_id
		 	 select array(SELECT json_array_elements_text(json_extract_path(possible_srvc_prvdr.form_data,'srvc_possible_prvdrs')))
		 	   from cl_cf_service_types as  possible_srvc_prvdr
		 	  where possible_srvc_prvdr.service_type_id = srvc_type_id_
		 	   into possible_srvc_prvdr;
		 	  
		 	  --if login usr org should be possible_srvc_prvdr
		 	  login_usr_org_should_possible_srvc_prvdr = (select org_id_ = any(possible_srvc_prvdr));
		 	  
		 	--Check if srvc_enable_srvc_prvdr_to_access_requests is enabled for the service_type_id
		 	 select srvc_prvdr_to_access_req.form_data->>'srvc_enable_srvc_prvdr_to_add_requests' 
		 	   from cl_cf_service_types as srvc_prvdr_to_access_req
		 	  where srvc_prvdr_to_access_req.service_type_id = srvc_type_id_
		 	   into srvc_prvdr_to_add_req;
		 	  
		     if  (_config_data->>'srvc_enable_srvc_prvdr')::bool is true and 
		     	 usr_organization_org_type = 'ORG_TYPE_SRVC_PRVDR' and 
		 	   	 srvc_prvdr_to_add_req is true and 
		 	     login_usr_org_should_possible_srvc_prvdr is true 
		 	     then
		 	    	org_id_ = srvc_type_id_org;
			 	 	form_data_ = form_data_::jsonb || ('{"org_id":"'|| org_id_ ||  '"}')::jsonb;
		 	 else
		 	  
			 		status = false;
					message = 'not_allowed_to_add_request';
					return json_build_object('status',status,'code',message,'data',resp_data);   
		 	 
		 	 end if; 
		 	
		end if;
	
		--get user_context_json
	    user_context_json = tms_get_user_context_from_data(form_data_);
	    user_context_json = jsonb_set(user_context_json::jsonb,'{srvc_type_id}',to_jsonb(form_data_->>'srvc_type_id'),true);
	   
		raise notice 'Trying to create a customer';

		-- Use customer ID from form data if provided (existing customer selected)
		-- Otherwise create customer if mobile number or customer name is provided
		IF form_data_->>'cust_id' is not null THEN
			cust_id_in_db = form_data_->>'cust_id';
		ELSIF (form_data_->>'cust_mobile' is not null AND length(form_data_->>'cust_mobile') = tms_hlpr_get_consumer_mobile_length_frm_org_lvl_settings(org_id_))
		   OR (form_data_->>'cust_full_name' is not null AND length(trim(form_data_->>'cust_full_name')) > 0) THEN
		   	cust_creation_resp = tms_create_cust(form_data_);
			cust_id_in_db = json_extract_path_text(cust_creation_resp->'data','entry_id');
		END IF;
--		raise notice 'Customer creato resp %', cust_creation_resp;
		
		-- srvc_type_prefix
		srvc_type_details_ = tms_get_srvc_type_details(org_id_,srvc_type_id_)->'data'->'form_data';
--		code_prefix = tms_get_srvc_type_details(org_id_,srvc_type_id_)->'data'->'form_data'->>'srvc_type_prefix';
		code_prefix = srvc_type_details_->>'srvc_type_prefix';
		
		if code_prefix is null then
			code_prefix = '';
		end if;
	
		if code_prefix = '' then
			custom_code_seperator = '';
		end if;
	
		srvc_type_prefix_custom_field = srvc_type_details_->>'srvc_type_prefix_custom_field';
		if srvc_type_prefix_custom_field is not null 
			and srvc_type_prefix_custom_field != '' 
			and length(form_data_->>srvc_type_prefix_custom_field) > 0 then 
			code_prefix = 
				code_prefix || custom_code_seperator || 
				(substring(form_data_->>srvc_type_prefix_custom_field from 1 for 10)) || '-';
			custom_code_seperator = '';
		end if;
	
		if (srvc_type_details_->>'srvc_type_include_customer_name_in_code')::bool is true 
			and length(form_data_->>'cust_full_name') > 0 then
			code_prefix = 
				code_prefix || custom_code_seperator ||
				upper(substring(split_part(form_data_->>'cust_full_name',' ',1) from 1 for 10)) ||
				'-';
		end if;
		
		code_suffix = to_char(clock_timestamp() at time zone 'utc', 'YYMMDDUS') :: text;
		display_code_ = code_prefix || code_suffix;

		-- Loop only if the display code exists
		WHILE tms_hlpr_check_if_display_code_exists(org_id_, srvc_type_id_, display_code_) LOOP
		    -- Increment code_suffix by 1
		    code_suffix = code_suffix::bigint + 1;
		    
		    -- Rebuild display_code_ with the incremented value
		    display_code_ = code_prefix || code_suffix::text;
		END LOOP;
	
		form_data_ = form_data_::jsonb || ('{"srvc_type_his_db_id":"'|| hlpr_tms_valid_and_create_srvc_type_his_or_rtn_ltst_his_db_id(form_data_, org_id_, srvc_type_id_) || '"}')::jsonb;
--		if (_config_data->>'srvc_req_cust_pincode_mandatory')::bool is true and (cust_pincode_ is null or cust_pincode_ = '')then
--			status = false;
--			message = 'Pincode is required';
--			return json_build_object('status',status,'code',message,'data','{}');
--		end if;

		-- Booking validation: if booking_details exists but no capacity_id, set booking_details to null and continue
--		if booking_details_ is not null and jsonb_typeof(booking_details_) = 'object' and jsonb_array_length(jsonb_path_query_array(booking_details_, '$.*')) > 0 then
--			if capacity_id_ is null then
--				-- If no capacity_id provided, clear booking_details and continue with request creation
--				booking_details_ = null;
--			end if;
--		end if;
		if feature_access_fr_TMS250414073641 then
			is_restrict_view_ = tms_hlpr_is_srvc_req_view_restricted('open', _config_data);
		end if;
		insert into public.cl_tx_srvc_req(
			org_id, srvc_type_id, status, priority, c_by, c_meta, form_data, display_code , collab_order_id, is_restricted, cust_pincode, cust_id
			)
			values (
				 org_id_, 
				 srvc_type_id_, 
				 'open', 
				 request_priority_, 
				 usr_id_, 
				 row(ip_address_,user_agent_,now() at time zone 'utc'), 
				 form_data_::jsonb,
				 display_code_,
				 collab_order_id_,
				 is_restrict_view_,
				 cust_pincode_,
				 cust_id_in_db			 
			)
			returning db_id into ins_id;
	
		get diagnostics affected_rows = ROW_COUNT;
--		raise notice 'Affected rows - %',  affected_rows;
		if affected_rows = 1 then
			-- We need to create a history for status
			
			if creation_date_ is not null then
				transition_date_ = creation_date_;
			else
				transition_date_ = now() at time zone 'utc';
			end if;
	
--			raise notice 'Trying to create transition log %', transition_date_;
			transition_log_res = tms_create_srvc_txn_log(ins_id,'open',transition_date_,srvc_type_id_,'',form_data_,'');
			
			--- Create history for timeline
			history_res = tms_add_to_srvc_req_timeline(srvc_type_id_, ins_id, 'CREATE','Created the request', form_data_);
		
			if line_items_revisions_data is not null then
				PERFORM tms_add_to_srvc_req_history(ins_id, srvc_req_his_type_, line_items_revisions_data);
			end if;
			--Update brand_authorities & prvdr_authorities column from srvc_req tbl
		    perform tms_hlpr_update_srvc_req_authorities(ins_id,form_data_);
			
			status = true;
			message = 'success';
			resp_data =  json_build_object('entry_id',ins_id,'display_code',display_code_,'cust_org_id',org_id_,'form_data',form_data_);
		
			--Initial status update ->  call tms_get_user_context_from_data for user_context_json data 
			--And set new_status or call same function itself
			if form_data_->>'initial_status' is not null then 
				 initial_status_update_json = jsonb_set(user_context_json::jsonb,'{new_status}',to_jsonb(form_data_->>'initial_status'),true);
				 PERFORM tms_create_service_request(initial_status_update_json,ins_id);
			end if;

			-- Call tms_wrkflw_srvc_req for
			--1. Assign service provider
			form_data_ = jsonb_set(form_data_::jsonb,'{usr_org_id}',to_jsonb(usr_org_id),true);
			form_data_ = jsonb_set(form_data_::jsonb,'{ins_id}',to_jsonb(ins_id),true);
			wrkflw_resp_data = tms_wrkflw_srvc_req(form_data_, _config_data);

			-- Auto-allot slot logic: if form_data has capacity_id, call tms_create_booking
			if form_data_->'booking_data'->>'capacity_id' is not null then
			  raise notice 'formifffffffffffff';
				PERFORM tms_create_booking(ins_id, form_data_);
			end if;
			--return '{}';
		    --Assign service provider start (function name should be this ->  tms_wrkflw_srvc_req(_form_data, _config_data, user_context) )
		    --check
			    --1st srvc_enable_srvc_prvdr is should be enable
			    --2nd default srvc_prvdr should be in possible_srvc_prvdr
			    --3nd srvc_default_provider is assigned ?
			    --4rd if is_cust_access = 1 then new_prvdr set usr_org_id
			    
			 --get possible_srvc_prvdr for srvc_type_id
--		 	 select array(SELECT json_array_elements_text(json_extract_path(possible_srvc_prvdr.form_data,'srvc_possible_prvdrs')))
--		 	   from cl_cf_service_types as  possible_srvc_prvdr
--		 	  where possible_srvc_prvdr.service_type_id = srvc_type_id_
--		 	   into possible_srvc_prvdr;
----		 	  raise notice 'possible_srvc_prvdr %', possible_srvc_prvdr;
--
--		 	  default_srvc_prvdr = _config_data->>'srvc_default_provider';
--		 	  default_srvc_prvdr_should_be_in_the_possible_srvc_prvdr = (select default_srvc_prvdr = any(possible_srvc_prvdr));
----		 	  raise notice 'default_srvc_prvdr_should_be_in_the_possible_srvc_prvdr %', default_srvc_prvdr_should_be_in_the_possible_srvc_prvdr;
--		 	 
--			if (
--			  		( 
--			  			(_config_data->>'srvc_enable_srvc_prvdr')::bool is true and 
--			  			 default_srvc_prvdr_should_be_in_the_possible_srvc_prvdr is true and
--			  			 (_config_data->>'srvc_default_provider' is not null and _config_data->>'srvc_default_provider' <> '' )
--			  		) 
--			  		or 
--			  		is_cust_access = '1'
--			   ) then 
--			   
--			   	  if is_cust_access = '1' then 
--			   	   		assigned_srvc_prvdr_json = jsonb_set(user_context_json::jsonb,'{new_prvdr}',to_jsonb(usr_org_id),true);
--			   	  else
--			   	  	assigned_srvc_prvdr_json = jsonb_set(user_context_json::jsonb,'{new_prvdr}',to_jsonb(_config_data->>'srvc_default_provider'),true);
--				  end if;
--				 
--				  PERFORM tms_create_service_request(assigned_srvc_prvdr_json,ins_id);
--		    end if;
		   --Assign service provider end
		  
		end if;
	
	elsif entry_id > 0 then
		form_data_ = jsonb_set(form_data_::jsonb,'{entry_id}',to_jsonb(entry_id),true);
		existing_db_row = tms_get_srvc_req_details(form_data_);
		existing_form_data = existing_db_row::jsonb->'data'->'form_data';
		display_code_ = existing_db_row::jsonb->'data'->>'title';
	
	    
		
		--geting cust_mobile and store in variable 
	    existing_cust_mob_no = existing_form_data->>'cust_mobile';
	   
	   	if _temp_is_agent_update is true then
	   		if is_org_srvc_prvdr_ is false and tms_hlpr_usr_right_to_access_service_routes(usr_id_,array['UPDATE','CREATE'],srvc_type_id_::text) is not true then
	   			status = false;
	   			message = 'no_update_access';
	   			return json_build_object('status',status,'code',message,'data','{}');
	   		end if;
	   	
	   		if is_org_srvc_prvdr_ is true and tms_hlpr_is_srvc_req_view_restricted(existing_db_row::jsonb->'data'->'status'->>'key', _config_data) is true then	   			 
	   			status = false;
	   			message = 'is_restricted';
	   			return json_build_object('status',status,'code',message,'data','{}');
	   		end if;
	   	end if;
	   
	   	if tms_hlpr_usr_has_access_to_del_srvc_req(usr_id_) is not true 
	   		and (form_data_ ->> 'update_deletion')::boolean is true 
	   		and form_data_->>'is_api_call' is null then
	   			status = false;
	   			message = 'no_delete_access';
	   			return json_build_object('status',status,'code',message,'data','{}');
		end if;
		
		FOR key_, value_ IN
	       	SELECT * FROM jsonb_each(form_data_::jsonb)
	    LOOP
	       -- Modifying only the fields sent to update
	    	existing_form_data = jsonb_set(existing_form_data::jsonb,('{' || key_ || '}')::text[],to_jsonb((form_data_->key_)),true);
--	       	RAISE NOTICE '%: %', key_, jsonb_typeof((form_data_->key_)::jsonb);
	    END LOOP;
	   
	    -- Use customer ID from form data if provided (existing customer selected)
		-- Otherwise create customer if mobile number is provided and existing record doesn't have one,
		-- or if customer name is provided
		IF form_data_->>'cust_id' is not null THEN
			cust_id_in_db = form_data_->>'cust_id';
		ELSIF (form_data_->>'cust_mobile' is not null and existing_cust_mob_no is null)
		   OR (form_data_->>'cust_full_name' is not null AND length(trim(form_data_->>'cust_full_name')) > 0) THEN
			cust_creation_resp = tms_create_cust(existing_form_data);
			cust_id_in_db = json_extract_path_text(cust_creation_resp->'data','entry_id');
		END IF;
	
		--sync price with master
		-- if srvc req created and assigned or srvc_type not configuration from provided side like (cl_tx_orgs_settings)
		-- check if org is srvc_prvdr and existing_form_data wich sp_srvc_type_his_db_id is not exists then call this function and save srvc_type_his_db_id [ hlpr_tms_valid_and_create_srvc_type_his_or_rtn_ltst_his_db_id ]
		if existing_form_data->>'sp_srvc_type_his_db_id' is null and tms_hlpr_is_org_srvc_prvdr_by_srvc_req_and_org_id(org_id_, entry_id) is true then 
			sp_srvc_type_his_db_id = hlpr_tms_valid_and_create_srvc_type_his_or_rtn_ltst_his_db_id(form_data_, org_id_, srvc_type_id_, true);
		    if sp_srvc_type_his_db_id is not null then
				existing_form_data = existing_form_data::jsonb || ('{"sp_srvc_type_his_db_id":"'|| sp_srvc_type_his_db_id || '"}')::jsonb;
			end if;
		
		elseif existing_form_data->>'srvc_type_his_db_id' is null and tms_hlpr_is_org_srvc_prvdr_by_srvc_req_and_org_id(org_id_, entry_id) is false then 
		
			existing_form_data = existing_form_data::jsonb || ('{"srvc_type_his_db_id":"'|| hlpr_tms_valid_and_create_srvc_type_his_or_rtn_ltst_his_db_id(form_data_, org_id_, srvc_type_id_) || '"}')::jsonb;
		
		end if;
	
		--if click on sync_prc button then get latest srvc_type_his_db_id from srvc_type history tbl and set form_data
		if form_data_->>'srvc_req_sync_prc' is not null then
			existing_form_data = existing_form_data::jsonb || ('{"srvc_type_his_db_id":"'|| hlpr_tms_valid_and_create_srvc_type_his_or_rtn_ltst_his_db_id(form_data_, org_id_, srvc_type_id_) || '"}')::jsonb;	
		
		elseif form_data_->>'sp_srvc_req_sync_prc' is not null then
		
			existing_form_data = existing_form_data::jsonb || ('{"sp_srvc_type_his_db_id":"'|| hlpr_tms_valid_and_create_srvc_type_his_or_rtn_ltst_his_db_id(form_data_, org_id_, srvc_type_id_, true) || '"}')::jsonb;	
		end if;
	
		profit_and_loss_data_ = form_data_->'profit_and_loss';
	
	     -- Auto-allot slot logic: if form_data has capacity_id, call tms_create_booking
	--   raise notice 'capacity_id_ %',capacity_id_; 
--		if capacity_id_ is not null then
--		      raise notice 'capacity_id_ %',capacity_id_; 
--			PERFORM tms_create_booking(entry_id, form_data_);
--		end if;
		
		raise notice 'Existing row - %', existing_db_row::jsonb->'data'->'form_data';
		raise notice 'Existing row - %', existing_form_data	;
		raise notice 'Existing row - %', form_data_;
		if existing_form_data::jsonb = existing_db_row::jsonb->'data'->'form_data' 
		   and status_key_frm_form is null
		   and assign_to_srvc_prvdr is null
		   then
			raise notice 'No change in form data, niether is this is a status change';
			affected_rows = 0;
		else 
			raise notice 'Existing row in update - %', existing_db_row::jsonb->'data'->'form_data';
			if profit_and_loss_data_ is not null then
				existing_form_data = existing_form_data::jsonb - 'profit_and_loss';
			end if;
			-- 
			update public.cl_tx_srvc_req as srvc_req
			   set status = case 
			   		when status_key_frm_form is null then srvc_req.status
			   		else status_key_frm_form -- keep as is
			   		end,
		   	   	   priority = case 
			   		when request_priority_ is null then srvc_req.priority
			   		else request_priority_ -- keep as is
			   		end,
			   	   feedback_token = case 
			   		when _feedback_token is null then srvc_req.feedback_token
			   		else _feedback_token -- keep as is
			   		end,
			   	   srvc_prvdr = case 
			   	    when assign_to_srvc_prvdr is null then srvc_req.srvc_prvdr 
			   	    else assign_to_srvc_prvdr
			   	    end,
			   	   srvc_prvdr_assg_time = case 
			   	    when assign_to_srvc_prvdr is null then srvc_req.srvc_prvdr_assg_time 
			   	    else now() at time zone 'utc'
			   	    end,
			   	   is_deleted = case
                        when is_deleted_ is null then srvc_req.is_deleted  
                        else is_deleted_
                        end,                                      
                   d_meta = case
                        when is_deleted_ is null then srvc_req.d_meta  
                        else (row(ip_address_,user_agent_,now() at time zone 'utc'))::entry_c_meta
                               
                        end,
                   d_by = case
                        when is_deleted_ is null then srvc_req.d_by    
                        else usr_id_
                        end,
                 collab_order_id = case
                        when collab_order_id_ is null then srvc_req.collab_order_id  
                        else collab_order_id_
                        end,  
			   	   form_data = existing_form_data,
		   	       u_meta = row(ip_address_,user_agent_,now() at time zone 'utc'),
		           u_by = usr_id_,
		           profit_and_loss_data = case
                        when profit_and_loss_data_ is null then srvc_req.profit_and_loss_data  
                        else profit_and_loss_data_::jsonb
                        end,
                   is_restricted = case 
				   		when is_restrict_view_ is null then is_restricted
				   		else is_restrict_view_ -- keep as is
				   		end,
				   prvdr_vertical = case
				   		when vertical_id_ = 0 then null
                        when vertical_id_ is null then srvc_req.prvdr_vertical  
                        else vertical_id_
                        end,
				   prvdr_srvc_hub = case
						when prvdr_srvc_hub_ = 0 then null
                        when prvdr_srvc_hub_ is null then srvc_req.prvdr_srvc_hub  
                        else prvdr_srvc_hub_
                        end,
				   cust_pincode = case
                        when cust_pincode_ is null then srvc_req.cust_pincode  
                        else cust_pincode_
                        end,
                   capacity_id = case
                        when capacity_id_ = 0  then null
                        when capacity_id_ is null then srvc_req.capacity_id
                        else capacity_id_
                        end,
                   start_booking_id = case
                        when booking_id_ is null then srvc_req.start_booking_id
                        else booking_id_
                        end,
                   booking_details = case
                        when capacity_id_ = 0  then null
                        when is_booked_ is null then srvc_req.booking_details
                        when booking_details_ is null then srvc_req.booking_details
                        else booking_details_
                        end,
                   cust_id = case
                        when cust_id_in_db is null then srvc_req.cust_id
                        else cust_id_in_db
                        end	,
                   total_booked_qty = case
                        when total_booked_qty_ is null then srvc_req.total_booked_qty
                        else total_booked_qty_
                        end	     
			 where ( 
			 		 org_id = org_id_ -- The actual org that created it or
			 		 or
			 		 srvc_prvdr = org_id_ -- the service provider 
			 	   )
			   and srvc_type_id = srvc_type_id_
			   and db_id = entry_id;
			
			get diagnostics affected_rows = ROW_COUNT;
--			raise notice 'Affected rows - %',  affected_rows;
		end if;
	
		if affected_rows > 0 then
		raise notice 'capacity_id_ %',capacity_id_; 
			if status_key_frm_form is not null then
				transition_date_ = now() at time zone 'utc';
				raise notice 'Trying to create transition log from old status %', existing_db_row->'data'->'status'->'key';
				transition_log_res = tms_create_srvc_txn_log(
					entry_id,status_key_frm_form,transition_date_,srvc_type_id_,
					json_extract_path_text(existing_db_row->'data'->'status','key'),form_data_,'');
				
				--- Create history for timeline
				old_status_title = existing_db_row->'data'->'status'->'title';
				
				select title 
			      from cl_cf_srvc_statuses as srvc_status
			     where srvc_status.srvc_id = srvc_type_id_
		           and srvc_status.status_key = status_key_frm_form
		          into new_status_title;
		        if _temp_is_agent_update is true then
		        	history_res = tms_add_to_srvc_req_timeline(
					srvc_type_id_, entry_id, 'UPDATE','Moved from ' || old_status_title || ' to ' || new_status_title || ' By Agent', form_data_, 'status_transition');
		        else
					history_res = tms_add_to_srvc_req_timeline(
					srvc_type_id_, entry_id, 'UPDATE','Moved from ' || old_status_title || ' to ' || new_status_title , form_data_, 'status_transition');		        
		        end if;
				
			else

				if update_for_comment then					
					history_res = tms_add_to_srvc_req_timeline(
						srvc_type_id_, entry_id, 'UPDATE','Added comment ' || timeline_comment, form_data_);
				else					
					if form_data_->'feedback_data' is not null then
						history_res = tms_add_to_srvc_req_timeline(
							srvc_type_id_, entry_id, 'UPDATE','Rated the service', form_data_);
					elsif assign_to_srvc_prvdr is not null and is_sp_reassgined is false then
						history_res = tms_add_to_srvc_req_timeline(
								srvc_type_id_, entry_id, 'UPDATE','Assigned service provider', form_data_);
					elsif is_sp_reassgined is true then
						history_res = tms_add_to_srvc_req_timeline(
								srvc_type_id_, entry_id, 'UPDATE','Reassigned service provider', form_data_);			
					elsif form_data_->'notification_data' is not null then
						history_res = tms_add_to_srvc_req_timeline(
								srvc_type_id_, entry_id, 'UPDATE', form_data_->>'title' , form_data_);
					elsif is_deleted_ is not null then
						history_res = tms_add_to_srvc_req_timeline(
						srvc_type_id_, entry_id, 'DELETE','Request Deleted ' || display_code_ , form_data_);
					
						srvc_req_address = tms_extract_address(to_jsonb(existing_form_data) ,'cust_');	
						_delete_srvc_req_proto = jsonb_set(_delete_srvc_req_proto,'{srvc_req_id}',to_jsonb(entry_id),true);
						_delete_srvc_req_proto = jsonb_set(_delete_srvc_req_proto,'{srvc_req_address}',to_jsonb(srvc_req_address),true);
						subtask_deletion_resp_ = tms_hlrp_get_entry_id_vs_query_of_sbtsks_on_srvc_req_deletion(_delete_srvc_req_proto::json);
						perform tms_hlpr_delete_sbtsk_of_srvc_req(entry_id, srvc_type_id_, display_code_, form_data_);
						
					elseif discount_approval_status is not null then
						history_res = tms_add_to_srvc_req_timeline(
							srvc_type_id_, entry_id, 'UPDATE', 'Updated discount approval as ' || discount_approval_status, form_data_);
						
					elseif sp_discount_approval_status is not null then
						history_res = tms_add_to_srvc_req_timeline(
							srvc_type_id_, entry_id, 'UPDATE', 'SP, Updated discount approval as ' || sp_discount_approval_status, form_data_);
						
				    elseif send_for_discount_approval is true then
				    	history_res = tms_add_to_srvc_req_timeline(srvc_type_id_, entry_id, 'UPDATE','Sent for discount approval', form_data_);
				 
			    	elseif sp_send_for_discount_approval is true  then
			    		history_res = tms_add_to_srvc_req_timeline(srvc_type_id_, entry_id, 'UPDATE','SP, sent for discount approval', form_data_);
				 
			    	elseif form_data_->>'srvc_req_lock' is not null then
						if (form_data_->>'srvc_req_lock')::bool is true then
							srvc_req_lock_status = 'locked for billing';
						else
							srvc_req_lock_status = 'unlocked for billing';
						end if;
				    	history_res = tms_add_to_srvc_req_timeline(srvc_type_id_, entry_id, 'UPDATE', 'Request ' || srvc_req_lock_status, form_data_);
	    
			    	elseif form_data_->>'sp_srvc_req_lock' is not null then
						if (form_data_->>'sp_srvc_req_lock')::bool is true then
							srvc_req_lock_status = 'SP locked for billing';
						else
							srvc_req_lock_status = 'SP unlocked for billing';
						end if;
						history_res = tms_add_to_srvc_req_timeline(srvc_type_id_, entry_id, 'UPDATE', 'SP Request ' || srvc_req_lock_status, form_data_);
					
					elseif  form_data_->>'srvc_req_sync_prc' is not null then 
						history_res = tms_add_to_srvc_req_timeline(srvc_type_id_, entry_id, 'UPDATE', 'Price synced with master', form_data_);
					
					elseif  form_data_->>'sp_srvc_req_sync_prc' is not null then 
						history_res = tms_add_to_srvc_req_timeline(srvc_type_id_, entry_id, 'UPDATE', 'SP, Price synced with master', form_data_);
					elseif  (form_data_->>'is_bulk_update')::int > 0 then 
						history_res = tms_add_to_srvc_req_timeline(srvc_type_id_, entry_id, 'UPDATE', 'Bulk assigned authorities', form_data_);
					elseif vertical_id_ is not null then	
						if vertical_id_ > 0 then
							history_res = tms_add_to_srvc_req_timeline(srvc_type_id_, entry_id, 'UPDATE', 'SP Vertical mapped successfully', form_data_);
						elseif vertical_id_ = 0 and existing_db_row->'data'->>'prvdr_vertical' is not null then
							history_res = tms_add_to_srvc_req_timeline(srvc_type_id_, entry_id, 'UPDATE', 'SP Vertical detached', form_data_);
						end if;
					elseif prvdr_srvc_hub_ is not null then	
						-- if existing_db_row->'data'->>'prvdr_srvc_hub' is null then
						-- no need to add history
						if prvdr_srvc_hub_ > 0 then
							history_res = tms_add_to_srvc_req_timeline(srvc_type_id_, entry_id, 'UPDATE', 'SP Service hub mapped successfully', form_data_);
						elseif prvdr_srvc_hub_ = 0 and existing_db_row->'data'->>'prvdr_srvc_hub' is not null then
							history_res = tms_add_to_srvc_req_timeline(srvc_type_id_, entry_id, 'UPDATE', 'SP Service hub detached', form_data_);
						end if;

					elseif (form_data_->>'booking_cancelled')::boolean is true then
					 	history_res = tms_add_to_srvc_req_timeline(srvc_type_id_, entry_id, 'UPDATE', 'Booking cancelled due to provider change', form_data_);
					elseif is_booked_ is not null and booking_message_ is not null then
						-- Add timeline entry for booking status
						if is_booked_ = true then
							history_res = tms_add_to_srvc_req_timeline(srvc_type_id_, entry_id, 'UPDATE', booking_message_, form_data_);
						else
							history_res = tms_add_to_srvc_req_timeline(srvc_type_id_, entry_id, 'UPDATE', booking_message_, form_data_);
						end if;
					else
						if is_line_item_updated_ > 0 or _temp_is_bulk_line_items_update > 0 then
							form_data_ := jsonb_set(form_data_::jsonb, '{comment}', to_jsonb('Modified line items.'::text), true)::json;						
						end if;
						history_res = tms_add_to_srvc_req_timeline(
								srvc_type_id_, entry_id, 'UPDATE','Modified request data', form_data_);
					end if;
				end if;
			   
			    if form_data_->'booking_data'->>'capacity_id' is not null and is_booked_ is null then				      
					PERFORM tms_create_booking(entry_id, form_data_);
			    end if;

				if assign_to_srvc_prvdr is not null then
					perform tms_hlpr_update_srvc_req_vertical(entry_id, form_data_);
				elseif cust_pincode_ is not null then
					perform tms_ace_hlpr_update_srvc_req_hub(entry_id, form_data_);
				end if;
			
				if line_items_revisions_data is not null then
					PERFORM tms_add_to_srvc_req_history(entry_id, srvc_req_his_type_ , line_items_revisions_data);
				end if;
                if feature_access_fr_TMS250620631024  then                
					if  is_sp_reassgined is true then	
						PERFORM tms_hlpr_clear_booked_slots_from_form_data(entry_id, form_data_);
					end if;
                end if;
             
--               if form_data_->'booking_data'->>'capacity_id' is not null and is_booked_ is null then				      
--					PERFORM tms_create_booking(entry_id, form_data_);
--			    end if;
						
			end if;
				--Update brand_authorities & prvdr_authorities column from srvc_req tbl
		    	perform tms_hlpr_update_srvc_req_authorities(entry_id, form_data_);
		end if;
	
		status = true;
		message = 'success';
		resp_data =  json_build_object('entry_id',entry_id,'display_code',display_code_);
		
		if is_deleted_ is true and subtask_deletion_resp_ is not null then			
			resp_data = jsonb_set(to_jsonb(resp_data),'{entry_id_vs_query_fr_deletion}',to_jsonb(subtask_deletion_resp_));		
		end if;
	end if;
	
	
	return json_build_object('status',status,'code',message,'data',resp_data);
 
END;
$function$
;
