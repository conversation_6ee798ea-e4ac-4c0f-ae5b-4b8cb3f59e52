CREATE OR REPLACE FUNCTION public.tms_hlpr_clear_booked_slots_from_form_data(srvc_req_id_ integer, form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare
    -- Bare minimums
    status boolean;
    message text;
    resp_data json;
    -- Variables from form_data
    org_id_ integer;
    usr_id_ uuid;
    srvc_type_id_ integer;
    -- For processing
    user_context_json json;
    booking_clear_json json;

begin
    -- Initialize status and message
    status := false;
    message := 'Internal_error';

    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    srvc_type_id_ := (form_data_->>'srvc_type_id')::integer;
   
   -- Step 1: Cancel all bookings linked to this service request
	UPDATE cl_tx_bookings
	SET is_cancelled = true,
	    u_meta = row((form_data_->>'ip_address')::text, (form_data_->>'user_agent')::text, now() at time zone 'utc')
	WHERE order_id = srvc_req_id_
	  AND is_cancelled = false;
	
	-- Step 2: Release capacity back
	UPDATE cl_tx_capacity c
	SET booked_cap_in_minutes = booked_cap_in_minutes - b.booked_qty,
	    u_meta = row((form_data_->>'ip_address')::text, (form_data_->>'user_agent')::text, now() at time zone 'utc')
	FROM cl_tx_bookings b
	WHERE c.db_id = b.capacity_id
	  AND b.order_id = srvc_req_id_
	  AND b.is_cancelled = true;


    -- Get user context JSON
    user_context_json = tms_get_user_context_from_data(form_data_);
    user_context_json = jsonb_set(user_context_json::jsonb,'{srvc_type_id}',to_jsonb(srvc_type_id_),true);

    -- Initialize booking_clear_json with user_context_json
    booking_clear_json = user_context_json;

    -- Set booking-related fields to null for clearing
    booking_clear_json = jsonb_set(booking_clear_json::jsonb, '{booking_data}', 'null'::jsonb, true);
    booking_clear_json = jsonb_set(booking_clear_json::jsonb, '{request_req_date}', 'null'::jsonb, true);
    booking_clear_json = jsonb_set(booking_clear_json::jsonb, '{start_time}', 'null'::jsonb, true);
    booking_clear_json = jsonb_set(booking_clear_json::jsonb, '{end_time}', 'null'::jsonb, true);
    booking_clear_json = jsonb_set(booking_clear_json::jsonb, '{booked_slots}', 'null'::jsonb, true);
    booking_clear_json = jsonb_set(booking_clear_json::jsonb, '{select_week}', 'null'::jsonb, true);

    -- Set clearing flags for capacity_id and booking_details
    booking_clear_json = jsonb_set(booking_clear_json::jsonb, '{capacity_id}', to_jsonb(0::bigint), true);
    booking_clear_json = jsonb_set(booking_clear_json::jsonb, '{booking_details}', to_jsonb(''::text), true);
    booking_clear_json = jsonb_set(booking_clear_json::jsonb, '{total_booked_qty}', to_jsonb(0::text), true);

    -- Add booking_cancelled flag to indicate this is a booking cancellation due to provider change
    booking_clear_json = jsonb_set(booking_clear_json::jsonb, '{booking_cancelled}', 'true'::jsonb, true);
    -- Call tms_create_service_request to save the changes
    perform tms_create_service_request(booking_clear_json, srvc_req_id_);

    -- Sync total booked quantities for this service request after clearing
    PERFORM tms_hlpr_sync_booked_man_minutes_on_srvc_req(srvc_req_id_::bigint);

    status = true;
    message = 'success';
    resp_data = json_build_object(
        'srvc_req_id', srvc_req_id_,
        'booked_slots_cleared', true
    );

    return json_build_object('status', status, 'code', message, 'data', resp_data);
END;
$function$
;
