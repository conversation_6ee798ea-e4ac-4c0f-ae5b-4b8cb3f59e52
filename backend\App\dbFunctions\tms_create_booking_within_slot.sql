CREATE OR REPLACE FUNCTION public.tms_create_booking_within_slot(ins_id_ bigint, form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE
    status boolean := false;
    message text := 'Internal_error';
    resp_data json;

    org_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;
    capacity_id_ bigint;
    srvc_type_id_ int;
    booking_details_ json;

    manpower_ integer;
    job_duration_ integer;
    hub_travel_time_ integer := 20;
    total_duration_ integer;
    required_man_minutes_ integer;

    available_capacity_ integer;
    total_cap_in_minutes_ integer;
    booked_cap_in_minutes_ integer;
    remaining_capacity_ integer;

    existing_booking_ids_ int[]; -- Changed to an array of booking IDs
    existing_booking_id_ int;
    existing_booking_qty_ integer;
    existing_capacity_id_ bigint;
    booking_qty_ integer;
    booking_ids int[] := '{}'; -- To store new or updated booking IDs
    slot_day_ date;

    user_context_json json;
    booking_update_json json;

    -- Booking status variables for timeline
    is_booked boolean := false;
    booking_message text;
   
    -- From SR table
    order_label_                text;  -- display_code
    prev_form_data_             json;  -- whole SR form_data
    prev_booking_details_       json;

BEGIN
    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    ip_address_ := form_data_->>'ip_address';
    user_agent_ := form_data_->>'user_agent';
    srvc_type_id_ := form_data_->>'srvc_type_id';
    booking_details_ := form_data_->>'booking_data';
    capacity_id_ := (form_data_->'booking_data'->>'capacity_id')::bigint;
    slot_day_ := (SELECT MIN(key)::date
              FROM jsonb_object_keys((form_data_->'booking_data'->'booked_slots')::jsonb) key);
             
             
      -- Pull display_code and previous SR form_data up front
    SELECT s.display_code, s.form_data
      INTO order_label_, prev_form_data_
      FROM cl_tx_srvc_req s
     WHERE s.db_id = ins_id_;

    prev_booking_details_ := COALESCE(prev_form_data_->'booking_data', '{}'::json);
         

    IF ins_id_ IS NULL OR capacity_id_ IS NULL THEN
        is_booked := false;
        booking_message := 'Booking failed - ins_id and capacity_id are required';
        RETURN json_build_object('status', false, 'message', 'ins_id and capacity_id are required');
    END IF;

    -- Booking parameters
    manpower_ := COALESCE((form_data_->>'6b858839-f154-4573-9e9b-4b01ebeb44a7')::integer, 1);
    job_duration_ := COALESCE((form_data_->>'3151dffe-45c3-4c27-9008-0a01c0205374')::integer, 60);

    total_duration_ := job_duration_ + hub_travel_time_;
    required_man_minutes_ := total_duration_ * manpower_;

    -- Get available capacity
    SELECT available_capacity, total_cap_in_minutes, booked_cap_in_minutes
    INTO available_capacity_, total_cap_in_minutes_, booked_cap_in_minutes_
    FROM cl_tx_capacity
    WHERE db_id = capacity_id_
      AND utilization < 1
      AND usr_tmzone_day = slot_day_;

     -- If no row or capacity is zero -> fail, return previous booking details
    IF available_capacity_ IS NULL OR available_capacity_ = 0 THEN
        booking_message := 'Booking failed - Capacity not found or zero for selected slot/day';
        is_booked := false;

        -- Build response with previous booking details so UI/state doesn’t change
        resp_data := json_build_object(
            'booking_id', NULL,
            'capacity_id', capacity_id_,
            'booking_details', prev_booking_details_
        );

        -- Prepare a no-op SR update payload (keeps previous booking_data)
        user_context_json := json_build_object(
            'org_id', org_id_,
            'usr_id', usr_id_,
            'ip_address', ip_address_,
            'user_agent', user_agent_,
            'srvc_type_id', srvc_type_id_
        );
        booking_update_json := jsonb_set(user_context_json::jsonb, '{booking_id}', 'null'::jsonb, true);
        booking_update_json := jsonb_set(booking_update_json, '{capacity_id}', to_jsonb(capacity_id_), true);
    --    booking_update_json := jsonb_set(booking_update_json, '{booking_data}', to_jsonb(prev_booking_details_), true);
        booking_update_json := jsonb_set(booking_update_json, '{is_booked}', 'false'::jsonb, true);
        booking_update_json := jsonb_set(booking_update_json, '{booking_message}', to_jsonb(booking_message), true);

        PERFORM tms_create_service_request(booking_update_json, ins_id_::int);

        RETURN json_build_object('status', false, 'message', booking_message, 'data', resp_data);
    END IF;

    remaining_capacity_ := total_cap_in_minutes_ - booked_cap_in_minutes_;

    -- Step 1: Check if the required manpower can be accommodated in the available capacity
     IF manpower_ > available_capacity_ THEN
        booking_message := 'Booking failed - Insufficient capacity for requested manpower';
        is_booked := false;

        resp_data := json_build_object(
            'booking_id', NULL,
            'capacity_id', capacity_id_,
            'booking_details', prev_booking_details_
        );

        user_context_json := json_build_object(
            'org_id', org_id_,
            'usr_id', usr_id_,
            'ip_address', ip_address_,
            'user_agent', user_agent_,
            'srvc_type_id', srvc_type_id_
        );
        booking_update_json := jsonb_set(user_context_json::jsonb, '{booking_id}', 'null'::jsonb, true);
        booking_update_json := jsonb_set(booking_update_json, '{capacity_id}', to_jsonb(capacity_id_), true);
      --  booking_update_json := jsonb_set(booking_update_json, '{booking_data}', to_jsonb(prev_booking_details_), true);
        booking_update_json := jsonb_set(booking_update_json, '{is_booked}', 'false'::jsonb, true);
        booking_update_json := jsonb_set(booking_update_json, '{booking_message}', to_jsonb(booking_message), true);

        PERFORM tms_create_service_request(booking_update_json, ins_id_::int);

        RETURN json_build_object('status', false, 'message', booking_message, 'data', resp_data);
    END IF;

    -- Step 2: Get existing bookings for the ins_id_
    SELECT array_agg(db_id)
    INTO existing_booking_ids_
    FROM cl_tx_bookings
    WHERE order_id = ins_id_
      AND (is_cancelled IS NULL OR is_cancelled = false);

    -- Step 3: Handle booking update logic (cancel existing bookings and create new ones)
    IF existing_booking_ids_ IS NOT NULL AND array_length(existing_booking_ids_, 1) > 0 THEN
        -- Mark existing bookings as cancelled
        UPDATE cl_tx_bookings
        SET is_cancelled = true,
            u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
        WHERE db_id = ANY(existing_booking_ids_);

        -- Release capacity from cancelled bookings
        UPDATE cl_tx_capacity
        SET booked_cap_in_minutes = booked_cap_in_minutes - b.booked_qty,
            u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
        FROM cl_tx_bookings b
        WHERE cl_tx_capacity.db_id = b.capacity_id
          AND b.db_id = ANY(existing_booking_ids_);

        
     END IF;

    -- Step 4: Create new booking
    INSERT INTO cl_tx_bookings (
        capacity_id, order_id, order_label, booked_qty, is_cancelled, c_meta, u_meta
    ) VALUES (
        capacity_id_, ins_id_, order_label_, required_man_minutes_, false,
        row(ip_address_, user_agent_, now() at time zone 'utc'),
        row(ip_address_, user_agent_, now() at time zone 'utc')
    )
    RETURNING db_id INTO existing_booking_id_;

    -- Add new booking ID to the list
   -- booking_ids := array_append(booking_ids, existing_booking_id_);
   
    -- Step 5: Update capacity for the new booking
    UPDATE cl_tx_capacity
       SET booked_cap_in_minutes = booked_cap_in_minutes + required_man_minutes_,
           u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
     WHERE db_id = capacity_id_;
    
    -- Build booking message *after insert* so ID is available
	IF existing_booking_ids_ IS NOT NULL AND array_length(existing_booking_ids_, 1) > 0 THEN
	    booking_message := 'Booking updated successfully - old booking(s) cancelled, new booking created with Booking ID: ' 
	                       || existing_booking_id_;
	ELSE
	    booking_message := 'New booking created successfully with Booking ID: ' 
	                       || existing_booking_id_;
	END IF;

    -- Set status and message
    status := true;
    is_booked := true;

    -- Prepare booking details JSON
    resp_data := json_build_object(
        'booking_id', booking_ids,
        'capacity_id', capacity_id_,
        'booking_details', booking_details_
    );

    -- Prepare user context for updating service request
    user_context_json := json_build_object(
        'org_id', org_id_,
        'usr_id', usr_id_,
        'ip_address', ip_address_,
        'user_agent', user_agent_,
        'srvc_type_id', srvc_type_id_
    );

    booking_update_json := jsonb_set(user_context_json::jsonb, '{booking_id}', to_jsonb(booking_ids), true);
    booking_update_json := jsonb_set(booking_update_json::jsonb, '{capacity_id}', to_jsonb(capacity_id_), true);
    booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_data}', to_jsonb(resp_data->'booking_details'), true);
    booking_update_json := jsonb_set(booking_update_json::jsonb, '{is_booked}', to_jsonb(is_booked), true);
    booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_message}', to_jsonb(booking_message), true);
	booking_update_json := jsonb_set(booking_update_json::jsonb, '{start_booking_id}', to_jsonb(existing_booking_id_), true);
    raise notice 'booking_update_json %',booking_update_json;
    -- Call to update service request
    PERFORM tms_create_service_request(booking_update_json, ins_id_::int);

    -- Sync total booked quantities for this service request
    PERFORM tms_hlpr_sync_booked_man_minutes_on_srvc_req(form_data_,ins_id_);

    -- Return final status and message
    RETURN json_build_object('status', status, 'message', booking_message, 'data', resp_data);

END;
$function$
;
